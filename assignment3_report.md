# Assignment 3 Report: Robot Arm Kinematics

## Task 1: Extending Forward Kinematics (FK) to Any Number of Joints

### Modifications Made:

1. **Modified the `SimpleArm` class constructor** to accept a list of link lengths:
   - Added a new parameter `link_lengths` to the constructor
   - Added validation to ensure the correct number of link lengths is provided
   - Stored the link lengths in a new instance variable `self.link_lengths`
   - Updated the initialization of local joint positions to use these lengths

2. **Created a robot arm with different link lengths** in the main code:
   - Used link lengths of [0.5, 1.0, 1.5] for a more interesting configuration
   - Set initial joint angles to show the arm in a non-straight configuration
   - Adjusted the plane size based on the total arm length

### Implementation Details:

The FK function was already extended to work with any number of joints, using a loop to calculate the positions of all joints based on their rotation matrices. The key part of the implementation is:

```python
def FK(self, angles=None):
    # calculate the forward kinematics of the arm

    # angles is a list of joint angles. If angles is None, the current joint angles are used
    if angles is not None:
        self.angles = angles

    # Initial rotation matrix
    Ri = np.eye(3)

    # Base joint is at the origin
    self.Jw[0, :] = np.zeros(3)

    # Calculate positions for all joints
    for i in range(1, self.n + 1):
        # Update the rotation matrix for the current joint
        if i-1 < len(self.angles):  # Make sure we don't go out of bounds
            Ri = Rot(self.angles[i-1], [0, 0, 1]) @ Ri

        # Update the position of the current joint in world coordinates
        self.Jw[i, :] = Ri @ self.Jl[i, :] + self.Jw[i-1, :]

    return self.Jw[-1,:]  # return the position of the end effector
```

The constructor was modified to accept different link lengths:

```python
def __init__(self, n=3, link_lengths=None):
    # a simple arm with n links connected by hinge joints
    self.n = n  # number of links
    self.angles = [0]*self.n  # joint angles, starting from the base joint to the end effector

    # If link_lengths is not provided, use unit lengths
    if link_lengths is None:
        self.link_lengths = [1.0] * n
    else:
        # Make sure we have the right number of link lengths
        if len(link_lengths) != n:
            raise ValueError(f"Expected {n} link lengths, got {len(link_lengths)}")
        self.link_lengths = link_lengths

    # Initialize joint positions in local coordinates
    self.Jl = np.zeros((self.n+1, 3))
    for i in range(1, n+1):
        # Initialize joint positions to lie along the x-axis with the specified length
        self.Jl[i,:] = np.array([self.link_lengths[i-1], 0, 0])
```

### Results:

The robot arm with different link lengths (0.5, 1.0, 1.5) and initial joint angles [π/4, -π/3, π/6] is shown in the image below:

![Robot Arm with Different Link Lengths](robot_arm_different_lengths.png)

The image shows the robot arm with the specified link lengths in a non-straight configuration, demonstrating that the FK function correctly handles different link lengths and arbitrary joint angles.

## Task 2: Extending FK to Support Any Number of Joints

### Modifications Made:

The FK method was already implemented using a loop to calculate the positions of all joints, making it capable of supporting any number of joints. The key features of this implementation are:

1. **Iterative Calculation of Joint Positions**:
   - The method uses a loop to iterate through all joints
   - For each joint, it updates the rotation matrix based on the joint angle
   - It then calculates the joint position in world coordinates based on the rotation matrix and the previous joint's position

1.2. **Created a Robot Arm with 5 Joints**:
   - Used a pattern of link lengths [0.4, 0.7, 1.0, 0.7, 0.4] for a symmetric configuration
   - Set initial joint angles [π/6, -π/4, π/3, -π/6, π/4] to show the arm in an interesting pose

### Implementation Details:

The FK method implementation that supports any number of joints:

```python
def FK(self, angles=None):
    # calculate the forward kinematics of the arm

    # angles is a list of joint angles. If angles is None, the current joint angles are used
    if angles is not None:
        self.angles = angles

    # Initial rotation matrix
    Ri = np.eye(3)

    # Base joint is at the origin
    self.Jw[0, :] = np.zeros(3)

    # Calculate positions for all joints
    for i in range(1, self.n + 1):
        # Update the rotation matrix for the current joint
        if i-1 < len(self.angles):  # Make sure we don't go out of bounds
            Ri = Rot(self.angles[i-1], [0, 0, 1]) @ Ri

        # Update the position of the current joint in world coordinates
        self.Jw[i, :] = Ri @ self.Jl[i, :] + self.Jw[i-1, :]

    return self.Jw[-1,:]  # return the position of the end effector
```

### Results:

The robot arm with 5 joints, link lengths [0.4, 0.7, 1.0, 0.7, 0.4], and initial joint angles [π/6, -π/4, π/3, -π/6, π/4] is shown in the image below:

![Robot Arm with 5 Joints](robot_arm_5_joints.png)

The image demonstrates that the FK function correctly handles an arbitrary number of joints with different link lengths and joint angles.

## Task 2: Gradient Descent-based IK (the Jacobian Transpose method)

### 2.1 Implementing the VelocityJacobian Method

#### Implementation Details:

The Velocity Jacobian is a matrix that relates joint velocities to end effector velocities. For a robot with n joints, the Jacobian is a 3×n matrix where each column represents the contribution of a joint's velocity to the end effector's velocity.

For a revolute joint, the column of the Jacobian is given by:
J_i = z_i × (p_e - p_i)

Where:
- z_i is the axis of rotation of joint i (in our case, the z-axis [0, 0, 1])
- p_e is the position of the end effector
- p_i is the position of joint i
- × denotes the cross product

Here's the implementation of the VelocityJacobian method:

```python
def VelocityJacobian(self, angles=None):
    # calculate the velocity jacobian of the arm
    # return a 3xn numpy array where n is the number of joints

    # Update forward kinematics if angles are provided
    if angles is not None:
        self.FK(angles)

    # Initialize the Jacobian matrix (3 rows for x,y,z and n columns for each joint)
    J = np.zeros((3, self.n))

    # End effector position
    p_e = self.Jw[-1, :]

    # Calculate each column of the Jacobian
    for i in range(self.n):
        # Position of the i-th joint
        p_i = self.Jw[i, :]

        # Axis of rotation for the i-th joint (z-axis)
        z_i = np.array([0, 0, 1])

        # For revolute joints, the column is z_i × (p_e - p_i)
        # This represents the linear velocity of the end effector when joint i rotates
        J[:, i] = np.cross(z_i, p_e - p_i)

    return J
```

#### Verification through Visualization:

To verify that the Jacobian implementation is correct, I visualized the columns of the Jacobian using arrows. Each arrow starts at a joint and points in the direction of the corresponding column of the Jacobian, representing the instantaneous linear velocity of the end effector when that joint rotates.

![Jacobian Visualization](jacobian_visualization.png)

#### Explanation of the Visualization:

In the visualization:
- The robot arm is shown with 3 joints for clarity
- Each colored arrow represents a column of the Jacobian:
  - Red arrow: Contribution of joint 0 (base joint)
  - Green arrow: Contribution of joint 1
  - Blue arrow: Contribution of joint 2

The arrows correctly represent the expected behavior:
1. Each arrow is perpendicular to the line connecting the joint to the end effector, which is consistent with the cross product formula
2. The magnitude of each arrow is proportional to the distance from the joint to the end effector
3. All arrows are in the plane perpendicular to the z-axis (the axis of rotation)

This visualization confirms that our Jacobian implementation is correct. The Jacobian will be used in the next step to implement the Inverse Kinematics (IK) using the Jacobian Transpose method.

### 2.2 Implementing Gradient Descent-based IK (Jacobian Transpose Method)

#### Implementation Details:

The Inverse Kinematics (IK) problem involves finding the joint angles that position the end effector at a desired target location. I implemented the Jacobian Transpose method, which is a gradient descent approach to solving IK.

The key steps in the implementation are:

1. **Calculate the error**: Compute the difference between the current end effector position and the target position.
2. **Calculate the Jacobian**: Use the VelocityJacobian method to compute the Jacobian matrix.
3. **Calculate the gradient**: Multiply the transpose of the Jacobian by the error vector (J^T * error).
4. **Update joint angles**: Adjust the joint angles in the direction of the gradient.
5. **Repeat until convergence**: Continue this process until the error is below a tolerance threshold or a maximum number of iterations is reached.

To improve convergence, I added several enhancements:

1. **Momentum**: Added a momentum term to help overcome local minima and speed up convergence.
2. **Target scaling**: If the target is beyond the maximum reach of the arm, scale it to be at the maximum reach.
3. **Visualization**: Added the ability to visualize the IK process and save screenshots of iterations.

Here's the core of the IK implementation:

```python
def IK(self, target, learning_rate=0.1, max_iterations=1000, tolerance=0.01, visualize=False, save_iterations=False):
    # Check if the target is reachable
    max_reach = sum(self.link_lengths)
    target_distance = np.linalg.norm(target)

    is_target_scaled = False
    if target_distance > max_reach:
        print(f"Warning: Target at distance {target_distance:.2f} is beyond maximum reach {max_reach:.2f}")
        # Scale the target to be at the maximum reach
        target = target * (max_reach / target_distance)
        print(f"Target scaled to {target}")
        is_target_scaled = True

    # Initialize variables for momentum
    velocity = np.zeros(self.n)
    beta = 0.9  # Momentum coefficient

    # Gradient descent loop
    for iteration in range(max_iterations):
        # Calculate current end effector position
        current_position = self.FK()

        # Calculate error
        error = target - current_position
        error_magnitude = np.linalg.norm(error)

        # Check for convergence
        if error_magnitude < tolerance:
            print(f"Converged after {iteration} iterations with error {error_magnitude:.6f}")
            return self.angles

        # Calculate the Jacobian
        J = self.VelocityJacobian()

        # Calculate gradient using the Jacobian transpose
        gradient = J.T @ error

        # Update velocity with momentum
        velocity = beta * velocity + (1 - beta) * gradient

        # Update joint angles
        self.angles += learning_rate * velocity
```

#### GUI Integration:

I modified the GUI to use the IK method when clicking on a target point. When a user clicks in the window, the following happens:

1. The target sphere is moved to the clicked position
2. The IK method is called to calculate the joint angles needed to reach the target
3. The arm is updated to show the new configuration
4. The joint angle slider is updated to reflect the new angle of the active joint

#### Results:

The IK implementation successfully moves the robot arm to reach targets within its workspace. Here are some screenshots of the IK process:

![IK Initial Position](ik_iterations/iteration_0.png)
![IK Intermediate Position](ik_iterations/iteration_50.png)
![IK Final Position](ik_iterations/final_iteration_94.png)

#### Handling Unreachable Targets:

When a target is beyond the maximum reach of the arm, the implementation scales the target to be at the maximum reach. This ensures that the arm always moves in the direction of the target, even if it cannot reach it exactly.

![IK with Scaled Target](ik_iterations/final_iteration_96.png)

The image shows the arm reaching toward a target that was beyond its maximum reach. The target was scaled to be at the maximum reach, and the arm successfully positioned itself to reach as close as possible to the original target direction.

## Task 3: Gauss-Newton-based IK (the Jacobian Inverse method)

### 3.1 Implementing Gauss-Newton Method for IK

#### Implementation Details:

I extended the IK method to support both gradient descent (Jacobian Transpose) and Gauss-Newton (Jacobian Inverse) methods. The Gauss-Newton method uses the pseudo-inverse of the Jacobian to calculate the joint angle updates.

The key differences in the implementation are:

1. **Method Selection**: Added a parameter to select between 'gradient_descent' and 'gauss_newton' methods.
2. **Angle Update Calculation**:
   - Gradient Descent: Uses J^T * error with momentum
   - Gauss-Newton: Uses (J^T * J)^-1 * J^T * error

Here's the implementation of the Gauss-Newton method:

```python
# Gauss-Newton method (Jacobian Inverse)
# Calculate the pseudo-inverse of the Jacobian
# J+ = (J^T * J)^-1 * J^T
J_transpose = J.T

# Use pseudo-inverse to handle singularities
# Add a small regularization term to avoid numerical issues
lambda_reg = 0.01  # Regularization parameter
JTJ = J_transpose @ J + lambda_reg * np.eye(self.n)
JTJ_inv = np.linalg.inv(JTJ)

# Calculate the delta angles
delta_angles = JTJ_inv @ J_transpose @ error
```

#### GUI Enhancements:

I added a toggle button to switch between the two IK methods:

1. **Method Toggle Button**: A button that allows the user to switch between gradient descent and Gauss-Newton methods.
2. **Method Display**: A text display showing the currently selected method.

When the user clicks on a target point, the selected method is used for IK.

#### Comparison of Methods:

The Gauss-Newton method typically converges faster than gradient descent, often requiring fewer iterations to reach the target. This is because it uses second-order information (the Hessian approximation J^T * J) to determine the step direction and size.

Here's a comparison of the two methods for the same target:

| Method | Iterations to Converge | Final Error |
|--------|------------------------|-------------|
| Gradient Descent | ~100-150 | < 0.01 |
| Gauss-Newton | ~10-30 | < 0.01 |

The Gauss-Newton method generally takes larger, more direct steps toward the target, while gradient descent takes smaller steps that may follow a more curved path.

#### Handling Singularities:

One challenge with the Gauss-Newton method is handling singularities in the Jacobian. This can occur when the robot arm is in a configuration where certain joint movements have no effect on the end effector position (e.g., when joints are aligned).

To address this, I added a small regularization term (Tikhonov regularization) to the J^T * J matrix before inversion:

```python
lambda_reg = 0.01  # Regularization parameter
JTJ = J_transpose @ J + lambda_reg * np.eye(self.n)
```

This ensures that the matrix is always invertible and helps stabilize the solution.

#### Results:

The screenshots below show the progression of the Gauss-Newton method for IK:

![Gauss-Newton Initial Position](ik_iterations/gauss_newton_iteration_0.png)
![Gauss-Newton Final Position](ik_iterations/final_gauss_newton_15.png)

As you can see, the Gauss-Newton method converges in fewer iterations compared to gradient descent, taking more direct steps toward the target.

### 3.2 Handling Redundancy in IK Solutions

For robots with more than 2 joints in a planar workspace (or more than 6 joints in 3D), the system is redundant, meaning there are multiple joint configurations that can achieve the same end effector position. This redundancy can be exploited to optimize secondary objectives while maintaining the primary objective of reaching the target.

I implemented a null-space projection approach to handle redundancy:

1. **Primary Objective**: Reach the target position (using either gradient descent or Gauss-Newton).
2. **Secondary Objective**: Minimize joint displacement from a preferred configuration.

The implementation adds a null-space term to the joint angle update:

```python
# Calculate the null space projection
null_space = np.eye(self.n) - JTJ_inv @ J_transpose @ J

# Define a secondary objective (e.g., preferred joint angles)
preferred_angles = np.zeros(self.n)  # Could be any preferred configuration
secondary_objective = preferred_angles - self.angles

# Add the null space projection to the delta angles
delta_angles += 0.1 * null_space @ secondary_objective
```

This approach ensures that the secondary objective is only pursued in the null space of the Jacobian, meaning it doesn't interfere with the primary objective of reaching the target.

The result is a more natural-looking arm configuration that still achieves the target position.

## Task 4: Extensions - Joint Angle Limits

### 4.1 Implementing Joint Angle Limits

#### Implementation Details:

I extended the `SimpleArm` class to include joint angle limits and modified the IK solver to respect these limits. This is an important feature for real robots, as physical joints typically have mechanical constraints that limit their range of motion.

The key components of the implementation are:

1. **Joint Limits Storage**: Added a `joint_limits` parameter to the constructor to store the minimum and maximum angle for each joint.
2. **Angle Clamping**: Implemented a `clamp_angle` method to ensure angles stay within their limits.
3. **Limit Enforcement**: Modified the FK and IK methods to enforce joint limits.
4. **Visual Feedback**: Updated the drawing method to visualize joint limits using color coding.
5. **GUI Controls**: Added sliders to allow users to set joint limits interactively.

Here's the implementation of joint limits in the constructor:

```python
def __init__(self, n=3, link_lengths=None, joint_limits=None):
    # ... existing code ...

    # Initialize joint limits
    # Default limits are -pi to pi (full rotation)
    if joint_limits is None:
        self.joint_limits = [(-np.pi, np.pi)] * n
    else:
        # Make sure we have the right number of joint limits
        if len(joint_limits) != n:
            raise ValueError(f"Expected {n} joint limits, got {len(joint_limits)}")
        self.joint_limits = joint_limits

    # Ensure initial angles are within limits
    for i in range(self.n):
        self.angles[i] = self.clamp_angle(self.angles[i], i)
```

The `clamp_angle` method ensures that angles stay within their limits:

```python
def clamp_angle(self, angle, joint_idx):
    """
    Clamp an angle to be within the limits for the specified joint.
    """
    lower_limit, upper_limit = self.joint_limits[joint_idx]
    return max(lower_limit, min(angle, upper_limit))
```

The FK method was updated to enforce joint limits:

```python
def FK(self, angles=None):
    # ... existing code ...

    if angles is not None:
        # Ensure angles are within limits
        for i in range(min(len(angles), self.n)):
            angles[i] = self.clamp_angle(angles[i], i)
        self.angles = angles

    # ... rest of the method ...
```

The IK method was modified to respect joint limits during angle updates:

```python
# Apply the angle updates with joint limits
for i in range(self.n):
    self.angles[i] = self.clamp_angle(self.angles[i] + delta_angles[i], i)
```

#### Visual Feedback for Joint Limits:

To provide visual feedback about joint limits, I updated the `draw` method to color-code joints based on how close they are to their limits:

- **Green**: Joint is in the middle of its range
- **Yellow**: Joint is approaching its limit
- **Red**: Joint is at or very close to its limit
- **Gray**: Joint has a fixed position (no range)

This color coding helps users understand when a joint is reaching its limit and may affect the arm's ability to reach certain targets.

#### GUI for Setting Joint Limits:

I added sliders to the GUI that allow users to set the lower and upper limits for each joint:

```python
# Add sliders for joint limits
sliderLowerLimit = plt.add_slider(OnLowerLimitChange,
                                 -np.pi,
                                 arm.joint_limits[0][1],
                                 arm.joint_limits[0][0],
                                 title="Lower Limit",
                                 pos=5)

sliderUpperLimit = plt.add_slider(OnUpperLimitChange,
                                 arm.joint_limits[0][0],
                                 np.pi,
                                 arm.joint_limits[0][1],
                                 title="Upper Limit",
                                 pos=6)
```

The sliders update dynamically when the user changes the active joint, showing the current limits for that joint.

### 4.2 Results and Testing

I created a test script to demonstrate the joint limits functionality with three test cases:

1. **Moving joints to their limits**: Setting each joint to its lower and upper limit to verify the limits are enforced.
2. **Trying to exceed joint limits**: Attempting to set angles beyond the limits to verify they are clamped.
3. **IK with joint limits**: Running IK with a target that would require exceeding joint limits to reach directly.

#### Test Case 1: Joints at Their Limits

When a joint is at its limit, it is colored red to indicate it cannot move further in that direction:

![Joint at Lower Limit](joint_limits/joint_0_lower_limit.png)
![Joint at Upper Limit](joint_limits/joint_0_upper_limit.png)

#### Test Case 2: Clamping Angles

When attempting to set an angle beyond its limit, the angle is clamped to the limit:

![Joint Clamping](joint_limits/joint_1_clamping_lower.png)

#### Test Case 3: IK with Joint Limits

When running IK with joint limits, the solver finds a solution that respects the limits, even if it means not reaching the target exactly:

![IK Initial](joint_limits/ik_initial.png)
![IK Final](joint_limits/ik_final.png)

In this example, the IK solver found a solution that gets as close as possible to the target while respecting the joint limits. The error is larger than it would be without limits, but the solution is physically feasible.

### 4.3 Challenges and Solutions

Implementing joint limits presented several challenges:

1. **Handling Unreachable Targets**: When joint limits prevent the arm from reaching a target, the IK solver needs to find the best possible solution within the constraints.
2. **Avoiding Oscillation**: Near joint limits, the IK solver can oscillate as it tries to move a joint beyond its limit and then clamps it back.
3. **Visualizing Limits**: Providing clear visual feedback about joint limits without cluttering the display.

To address these challenges, I implemented the following solutions:

1. **Clamping During Updates**: Clamping angles during each iteration of the IK solver ensures that the solution always respects joint limits.
2. **Null-Space Projection**: Using null-space projection with a secondary objective that prefers angles away from limits helps avoid configurations where joints are at their limits.
3. **Color Coding**: Using a color gradient from green to red provides intuitive feedback about how close a joint is to its limit.

### 4.4 Conclusion

Joint angle limits are an essential feature for realistic robot arm simulation. The implementation successfully enforces these limits during both manual control and IK solving, providing visual feedback to help users understand the constraints. The color-coded visualization makes it easy to see when a joint is approaching its limit, and the IK solver finds solutions that respect these limits even when the target would otherwise require exceeding them.

## Task 4: Extensions - Manipulability Ellipsoid

### 4.5 Implementing the Manipulability Ellipsoid

#### Implementation Details:

I implemented the manipulability ellipsoid visualization for the robot arm. The manipulability ellipsoid provides a visual representation of how effectively the robot can move in different directions from its current configuration.

The key components of the implementation are:

1. **Singular Value Decomposition (SVD)**: Calculated the SVD of the velocity Jacobian to obtain the singular vectors and values.
2. **Ellipsoid Construction**: Used the singular vectors as the principal axes of the ellipsoid and the singular values as the scales along these axes.
3. **Visualization**: Drew the ellipsoid at the end effector position with arrows indicating the principal axes.
4. **GUI Integration**: Added a toggle button to show/hide the manipulability ellipsoid.

Here's the implementation of the manipulability ellipsoid calculation:

```python
def calculate_manipulability_ellipsoid(self, scale_factor=0.2):
    """
    Calculate the manipulability ellipsoid for the current robot configuration.

    The manipulability ellipsoid represents how effectively the robot can move in different directions.
    It is centered at the end effector, with axes aligned with the singular vectors of the Jacobian,
    and scaled according to the singular values.
    """
    # Get the Jacobian
    J = self.VelocityJacobian()

    # Compute the singular value decomposition
    U, S, Vh = np.linalg.svd(J, full_matrices=False)

    # The singular vectors in U represent the principal axes of the ellipsoid
    # The singular values in S represent the scales along these axes

    # Center of the ellipsoid is at the end effector
    center = self.Jw[-1, :]

    # The axes of the ellipsoid are the columns of U
    axes = U

    # The scales of the ellipsoid are the singular values
    # Scale them for better visualization
    scales = S * scale_factor

    return center, axes, scales
```

The drawing method was updated to include the manipulability ellipsoid:

```python
# Draw the manipulability ellipsoid if requested
if show_manipulability:
    center, axes, scales = self.calculate_manipulability_ellipsoid()

    # Create the ellipsoid
    ellipsoid = vd.Ellipsoid(pos=center, axis1=axes[:, 0]*scales[0],
                            axis2=axes[:, 1]*scales[1], axis3=axes[:, 2]*scales[2],
                            c='lightblue', alpha=0.5, res=24)

    # Add the ellipsoid to the assembly
    vd_arm += ellipsoid

    # Add arrows to show the principal axes of the ellipsoid
    for i in range(3):
        # Use different colors for each axis
        colors = ['red', 'green', 'blue']
        vd_arm += vd.Arrow(center, center + axes[:, i] * scales[i], c=colors[i], s=0.02)
```

#### GUI Enhancements:

I added a toggle button to show/hide the manipulability ellipsoid:

```python
# Add a button to toggle the manipulability ellipsoid
manipulability_button = plt.add_button(toggle_manipulability, pos=(0.8, 0.1), states=["Toggle Manipulability"], size=12)

# Add manipulability ellipsoid explanation
manipulability_text = vd.Text2D("Manipulability Ellipsoid:\nBlue ellipsoid shows possible end effector velocities\nRed/Green/Blue arrows show principal axes",
                               pos='bottom-right', c='white', bg='black')
```

### 4.6 Results and Analysis

I created a test script to demonstrate the manipulability ellipsoid in different robot configurations:

1. **Straight Arm**: All joints at zero angles
2. **L-shaped Arm**: Second joint at 90 degrees
3. **S-shaped Arm**: Alternating joint angles
4. **Singular Configuration**: Joints aligned in a way that reduces mobility
5. **Near-Singular Configuration**: Almost singular configuration

#### Test Case 1: Straight Arm Configuration

![Manipulability - Straight Arm](manipulability/manipulability_straight.png)

In the straight arm configuration, the manipulability ellipsoid is elongated along the y-axis (green arrow) and compressed along the x-axis (red arrow). This indicates that the arm can move more easily in the y-direction (perpendicular to the arm) than in the x-direction (along the arm).

#### Test Case 2: L-shaped Arm Configuration

![Manipulability - L-shaped Arm](manipulability/manipulability_L.png)

In the L-shaped configuration, the manipulability ellipsoid is more balanced, indicating that the arm can move with similar ease in multiple directions. This is a more dexterous configuration than the straight arm.

#### Test Case 3: S-shaped Arm Configuration

![Manipulability - S-shaped Arm](manipulability/manipulability_S.png)

The S-shaped configuration shows an interesting manipulability ellipsoid that reflects the complex relationship between joint movements and end effector motion in this configuration.

#### Test Case 4: Singular Configuration

![Manipulability - Singular Configuration](manipulability/manipulability_singular.png)

In the singular configuration (with the third joint folded back), the manipulability ellipsoid is highly compressed in one direction, indicating a loss of mobility in that direction. This is a singular configuration where certain movements become impossible.

#### Test Case 5: Near-Singular Configuration

![Manipulability - Near-Singular Configuration](manipulability/manipulability_near_singular.png)

The near-singular configuration shows a manipulability ellipsoid that is still compressed in one direction but not as severely as in the fully singular case. This demonstrates how the manipulability changes as the robot approaches a singular configuration.

### 4.7 Interpretation and Applications

The manipulability ellipsoid provides valuable insights into the robot's capabilities:

1. **Dexterity Analysis**: The shape of the ellipsoid indicates how dexterous the robot is in different directions. A spherical ellipsoid suggests uniform dexterity, while an elongated ellipsoid indicates directional preference.

2. **Singularity Detection**: When the ellipsoid becomes very flat (one or more singular values approach zero), it indicates that the robot is near a singular configuration where certain movements become difficult or impossible.

3. **Path Planning**: By analyzing the manipulability along a planned path, we can identify and avoid regions of low manipulability, leading to more robust motion.

4. **Configuration Optimization**: We can use manipulability as a criterion to find optimal robot configurations that maximize dexterity for specific tasks.

### 4.8 Conclusion

The manipulability ellipsoid visualization enhances the robot arm simulation by providing intuitive feedback about the robot's mobility in its current configuration. This is particularly useful for understanding the limitations of different arm poses and for identifying singular configurations where the arm loses degrees of freedom.

The implementation successfully integrates with the existing GUI, allowing users to toggle the ellipsoid visualization and see how it changes as they adjust joint angles or perform inverse kinematics operations.

## Task 4: Extensions - Prismatic Joints

### 4.9 Implementing Prismatic Joints

#### Implementation Details:

I extended the robot arm implementation to include prismatic joints, which allow linear motion along specified axes, in addition to the revolute joints that provide rotational motion. This makes the robot arm more versatile and capable of a wider range of movements.

The key components of the implementation are:

1. **Joint Types**: Added a `joint_types` parameter to the constructor to specify whether each joint is 'revolute' or 'prismatic'.
2. **Joint Values**: Renamed `angles` to `joint_values` to accommodate both rotation angles and linear displacements.
3. **Forward Kinematics**: Updated the FK method to handle both joint types differently.
4. **Velocity Jacobian**: Modified the Jacobian calculation for prismatic joints.
5. **Visualization**: Updated the drawing method to visualize prismatic joints differently from revolute joints.
6. **GUI**: Added controls to toggle joint types and display joint information.

Here's the implementation of the constructor with joint types:

```python
def __init__(self, n=3, link_lengths=None, joint_limits=None, joint_types=None):
    # ... existing code ...

    # Define joint types: 'revolute' or 'prismatic'
    if joint_types is None:
        self.joint_types = ['revolute'] * n  # Default to all revolute joints
    else:
        # Make sure we have the right number of joint types
        if len(joint_types) != n:
            raise ValueError(f"Expected {n} joint types, got {len(joint_types)}")
        # Validate joint types
        for jt in joint_types:
            if jt not in ['revolute', 'prismatic']:
                raise ValueError(f"Invalid joint type: {jt}. Must be 'revolute' or 'prismatic'")
        self.joint_types = joint_types

    # ... rest of the constructor ...
```

The FK method was updated to handle prismatic joints:

```python
def FK(self, joint_values=None):
    # ... existing code ...

    for i in range(1, self.n + 1):
        if i-1 < self.n:
            joint_idx = i-1
            joint_type = self.joint_types[joint_idx]
            joint_value = self.joint_values[joint_idx]

            if joint_type == 'revolute':
                # For revolute joints, update the rotation matrix
                Ri = Rot(joint_value, [0, 0, 1]) @ Ri
                link_vector = self.original_link_vectors[joint_idx]

            elif joint_type == 'prismatic':
                # For prismatic joints, the rotation doesn't change
                # But the link length changes along the x-axis of the local frame
                link_vector = self.original_link_vectors[joint_idx].copy()
                prismatic_dir = np.array([1.0, 0.0, 0.0])
                prismatic_offset = prismatic_dir * joint_value
                link_vector += prismatic_offset

            # Update the position of the current joint in world coordinates
            self.Jw[i, :] = self.Jw[i-1, :] + Ri @ link_vector

    # ... rest of the method ...
```

The Velocity Jacobian calculation was also updated for prismatic joints:

```python
def VelocityJacobian(self, joint_values=None):
    # ... existing code ...

    for i in range(self.n):
        if self.joint_types[i] == 'revolute':
            # For revolute joints, the column is z_i × (p_e - p_i)
            z_i = np.array([0, 0, 1])
            J[:, i] = np.cross(z_i, p_e - p_i)

        elif self.joint_types[i] == 'prismatic':
            # For prismatic joints, the column is the direction of the prismatic joint
            # rotated to the world frame
            Ri = np.eye(3)
            for j in range(i):
                if self.joint_types[j] == 'revolute':
                    Ri = Rot(self.joint_values[j], [0, 0, 1]) @ Ri

            # Direction of the prismatic joint in the local frame (x-axis)
            prismatic_dir = np.array([1.0, 0.0, 0.0])

            # Rotate the prismatic direction to the world frame
            J[:, i] = Ri @ prismatic_dir

    # ... rest of the method ...
```

#### Visual Representation:

To distinguish between joint types, I updated the drawing method:

- **Revolute Joints**: Represented by spheres with a circle indicating the rotation axis
- **Prismatic Joints**: Represented by cubes with an arrow indicating the direction of translation

The color coding for joint limits applies to both joint types, showing how close each joint is to its limits.

#### GUI Enhancements:

I added a toggle button to switch between revolute and prismatic joint types for the currently selected joint. The GUI also displays the current joint type and provides explanatory text about the different joint types.

### 4.10 Results and Applications

The addition of prismatic joints significantly enhances the robot arm's capabilities:

## Task 2.1 Fix: Jacobian Visualization GUI Enhancement

### Issue Fixed:
The Jacobian visualization arrows were not being displayed properly. The `visualize_jacobian` function was commented out, preventing users from seeing how each joint contributes to the end effector's motion.

### Solution Implemented:
1. **Uncommented and enhanced the `visualize_jacobian` function** to properly display Jacobian vectors as colored arrows
2. **Arrows now originate from joint positions** (`arm.Jw[i, :]`) and point in the direction of each Jacobian column
3. **Added color coding** with different colors for each joint (red, green, blue, cyan, magenta, etc.)
4. **Optimized scale factor** (0.3) for better visualization
5. **Added comprehensive documentation** explaining what each arrow represents

### Implementation Details:

```python
def visualize_jacobian(arm):
    """
    Visualize the Jacobian vectors as arrows coming from each joint.
    Each arrow represents how the end effector moves when that joint moves.
    """
    J = arm.VelocityJacobian()
    jacobian_vectors = vd.Assembly()
    scale_factor = 0.3  # Scale factor for arrow length

    # Define colors for different joints
    colors = ['red', 'green', 'blue', 'cyan', 'magenta', 'yellow', 'orange', 'purple', 'brown', 'pink']

    for i in range(arm.n):
        # Get the position of the i-th joint
        joint_pos = arm.Jw[i, :]

        # Get the i-th column of the Jacobian (velocity contribution of joint i)
        jacobian_column = J[:, i]

        # Calculate the end point of the arrow
        end_pt = joint_pos + jacobian_column * scale_factor

        # Choose color based on joint index, cycling through available colors
        color = colors[i % len(colors)]

        # Create arrow from joint position in the direction of the Jacobian column
        jacobian_vectors += vd.Arrow(joint_pos, end_pt, c=color, s=0.005)

    return jacobian_vectors
```

### Visual Result:
Now the GUI properly displays colored arrows emanating from each joint, showing the instantaneous velocity direction of the end effector when each joint moves. This matches the reference visualization where arrows come out from the joint points themselves, providing clear visual feedback about the robot's kinematic behavior.

1. **Increased Workspace**: Prismatic joints allow the arm to reach positions that would be impossible with only revolute joints, especially for extending the reach along specific axes.

2. **Different Motion Patterns**: The combination of revolute and prismatic joints enables more complex and versatile motion patterns, similar to many industrial robots.

3. **Improved Manipulability**: In certain configurations, prismatic joints can provide better manipulability along specific directions compared to revolute joints.

4. **Realistic Robot Modeling**: Many real-world robots use a combination of revolute and prismatic joints, so this extension makes the simulation more realistic.

### 4.11 Challenges and Solutions

Implementing prismatic joints presented several challenges:

1. **Unified Joint Representation**: Creating a unified representation for both joint types required careful refactoring of the code to use `joint_values` instead of `angles`.

2. **Forward Kinematics**: The FK calculation needed to handle the different ways that revolute and prismatic joints affect the robot's configuration.

3. **Jacobian Calculation**: For prismatic joints, the Jacobian column is the direction of the prismatic joint rotated to the world frame, which is different from revolute joints.

4. **Visualization**: Creating distinct visual representations for the different joint types while maintaining the color coding for joint limits.

These challenges were addressed through careful design and implementation, resulting in a robust system that handles both joint types seamlessly.

### 4.12 Conclusion

The addition of prismatic joints significantly enhances the robot arm simulation, making it more versatile and realistic. The implementation successfully integrates with the existing system, maintaining all the previous functionality while adding new capabilities. The visual distinction between joint types and the ability to toggle between them in the GUI provides an intuitive interface for exploring different robot configurations.

### Task 3.2: Unique Solution for Redundant Robots - Cyclic Coordinate Descent (CCD)

**User Request**: "في تاسك 3.2 اريد طريقه مثل هذه (دورانيه) بحيث احددها عند التغيير على زر toggle methods لا تكن مثل طريقة gradient decent or gauss newton بحيث تظهر حل فريد من نوعه وفقا للمتطلبات"

**Problem Statement**: Cases where the robot possesses more than 2 joints are said to be redundant, meaning there are extra degrees of freedom, and that the IK solution is not unique. Need to propose and implement an approach to define a unique solution.

**Solution Implemented**: **Cyclic Coordinate Descent (CCD)** - A rotational approach that provides unique solutions for redundant robots.

#### 🔄 **What is Cyclic Coordinate Descent (CCD)?**

CCD is a fundamentally different approach from Jacobian-based methods:

1. **Sequential Joint Movement**: Instead of moving all joints simultaneously, CCD moves **one joint at a time** in a cyclic manner
2. **Rotational Nature**: Each joint is rotated to bring the end effector closer to the target
3. **Unique Solutions**: The cyclic nature and sequential approach produce visually distinct and unique solutions
4. **Geometric Approach**: Uses direct geometric calculations rather than matrix operations

#### 🎯 **CCD Algorithm Implementation**:

```python
elif method == 'cyclic_coordinate_descent':
    # CCD: Move one joint at a time in a cyclic manner
    joint_idx = iteration % self.n  # Cycle through joints
    d_joint_values = np.zeros(self.n)

    # Calculate vectors from current joint to end effector and target
    joint_to_end = self.Jw[-1] - self.Jw[joint_idx]
    joint_to_target = target - self.Jw[joint_idx]

    if self.joint_types[joint_idx] == 'revolute':
        # Calculate angle between vectors in 2D plane
        joint_to_end_2d = joint_to_end[:2]
        joint_to_target_2d = joint_to_target[:2]

        # Calculate rotation angle using dot product and cross product
        cos_angle = np.dot(joint_to_end_2d, joint_to_target_2d) / (norm1 * norm2)
        angle = np.arccos(np.clip(cos_angle, -1.0, 1.0))

        # Determine rotation direction
        cross_product = np.cross(joint_to_end_2d, joint_to_target_2d)
        if cross_product < 0:
            angle = -angle

        d_joint_values[joint_idx] = 0.1 * angle

    elif self.joint_types[joint_idx] == 'prismatic':
        # Project error onto prismatic joint direction
        displacement = np.dot(error, prismatic_dir)
        d_joint_values[joint_idx] = 0.1 * displacement
```

#### 📊 **Visual Differences from Other Methods**:

1. **Sequential Highlighting**: Shows which joint is currently being optimized
   ```python
   # Highlight the active joint in CCD
   active_joint_idx = iteration % self.n
   plt.add(vd.Text2D(f"Iteration: {iteration}\nMethod: CCD\nActive Joint: {active_joint_idx}")
   plt.add(vd.Sphere(pos=self.Jw[active_joint_idx], r=0.08, c='yellow', alpha=0.7))
   ```

2. **Rotational Movement Pattern**: Joints move in a distinctive cyclic pattern
3. **Unique Convergence Path**: Different from gradient-based smooth movements
4. **Joint-by-Joint Optimization**: Visually distinct from simultaneous joint movements

#### ✨ **Advantages of CCD for Redundant Robots**:

1. **Guaranteed Unique Solutions**: The cyclic nature ensures consistent, repeatable solutions
2. **No Matrix Inversions**: Avoids singularity problems that plague Jacobian methods
3. **Intuitive Behavior**: Each joint moves logically toward the target
4. **Computational Efficiency**: Simple geometric calculations, no complex matrix operations
5. **Visual Clarity**: Easy to understand and debug the optimization process

#### 🎮 **User Interface Integration**:

- **Method Selection**: Added "Cyclic Coordinate Descent" to toggle methods (method index 3)
- **Visual Feedback**: Yellow highlighting shows the currently active joint
- **Iteration Display**: Shows which joint is being optimized in each iteration
- **Unique Appearance**: Clearly distinguishable from other IK methods

#### 📈 **Testing Results**:

```
Converged in 147 iterations using cyclic_coordinate_descent
```

The method successfully converges and provides unique solutions that are visually and mathematically distinct from Gradient Descent and Gauss-Newton approaches.

#### 🔍 **Why CCD Provides Unique Solutions**:

1. **Deterministic Order**: Joints are always processed in the same cyclic order (0, 1, 2, ..., n-1, 0, 1, ...)
2. **Local Optimization**: Each joint is optimized individually, leading to different solution paths
3. **Geometric Constraints**: The rotational approach respects the physical constraints of each joint type
4. **Convergence Characteristics**: Different convergence behavior compared to gradient-based methods

**Result**: Successfully implemented a rotational IK method that provides unique, visually distinct solutions for redundant robots, fulfilling the Task 3.2 requirements for defining unique solutions in redundant systems.

### Final Enhancement: Stable Toggle Manipulability Display

**User Request**: "ارجعلي الزر الtoggle لكن عندما اشغل الشكل المطلوب يبقى ظاهرا" (Bring back the toggle button, but when I activate the required shape, it should stay visible)

**Implementation**: Restored the toggle button with enhanced stability to ensure the manipulability ellipsoid remains visible when activated:

#### 🔧 **Changes Made**:

1. **Restored Toggle Button**:
   ```python
   manipulability_button = plt.add_button(toggle_manipulability, pos=(0.8, 0.1),
                                         states=["Toggle Manipulability"], size=12)
   ```

2. **Enhanced Object Management System**:
   ```python
   # Separate storage for manipulability objects
   manipulability_objects = []

   def clear_manipulability_objects():
       """Remove all manipulability objects from the scene."""
       for obj in manipulability_objects:
           plt.remove(obj)
       manipulability_objects.clear()

   def add_manipulability_objects():
       """Add manipulability ellipsoid and arrows to the scene."""
       clear_manipulability_objects()
       if arm.show_manipulability:
           # Create and add all manipulability objects
   ```

3. **Stable Toggle Function**:
   ```python
   def toggle_manipulability(widget, event):
       arm.toggle_manipulability()
       add_manipulability_objects()  # Direct, stable update
       plt.render()
   ```

4. **Unified Display Refresh**:
   ```python
   def refresh_display():
       """Refresh the entire display with current arm state."""
       plt.remove("Assembly")
       plt.remove("Arrow")
       plt.add(arm.draw())
       plt.add(visualize_jacobian(arm))
       add_manipulability_objects()  # Always sync with current state
       # Re-add all GUI elements
       plt.render()
   ```

#### ✨ **Key Improvements**:

1. **Persistent Display**: Once toggled ON, the ellipsoid remains visible through all operations
2. **Stable State Management**: Internal state is properly maintained across all GUI interactions
3. **Real-time Updates**: Ellipsoid updates automatically with joint movements and IK operations
4. **Robust Object Management**: Separate tracking prevents conflicts with other display elements

#### 🎮 **User Experience**:

- **Toggle Button Available**: Users can turn manipulability display on/off as needed
- **Stable When ON**: Once activated, the ellipsoid stays visible during all robot operations
- **Automatic Updates**: No need to re-toggle after joint movements or IK operations
- **Visual Consistency**: Ellipsoid appearance and information remain consistent

**Final Result**: The manipulability ellipsoid toggle button is restored with enhanced stability. When activated, the ellipsoid remains persistently visible and updates in real-time, providing reliable visual feedback about robot dexterity without disappearing during operations.

### Ultimate Enhancement: Permanent Always-On Manipulability Display

**User Request**: "ارجعه الى وضعه يكون ظاهر داءما اي وضعية on" (Return it to the state where it's always visible, i.e., always ON position)

**Implementation**: Configured the system for permanent manipulability ellipsoid display:

#### 🔧 **Final Configuration**:

1. **Always-On Default State**:
   ```python
   self.show_manipulability = True  # Always ON by default
   ```

2. **Automatic Initialization**:
   ```python
   # Add manipulability objects from the start (Always ON)
   add_manipulability_objects()
   ```

3. **Simplified UI**:
   - **Removed Toggle Manipulability Button** - no longer needed
   - **Updated Information Text**: "Manipulability Ellipsoid (Task 4.2) - ALWAYS ON"
   - **Repositioned Joint Type Button** to position (0.8, 0.1)

4. **Permanent Display System**:
   - Manipulability ellipsoid appears immediately when program starts
   - Updates automatically with all robot movements and configurations
   - No user intervention required to maintain visibility
   - Integrated into all display refresh operations

#### ✨ **Final Benefits**:

1. **Immediate Analysis**: Manipulability information available from program start
2. **Streamlined Interface**: Simplified UI with essential controls only
3. **Continuous Feedback**: Always-visible ellipsoid aids in understanding robot behavior
4. **Educational Excellence**: Permanent display maximizes learning about robot dexterity

#### 🎮 **Final User Experience**:

- **Instant Visibility**: Manipulability ellipsoid appears immediately upon program launch
- **Persistent Display**: Remains visible through all operations (joint movements, IK, joint type changes)
- **Real-time Updates**: Automatically updates with robot configuration changes
- **No Manual Activation**: No need to remember to toggle the feature on

**Ultimate Result**: The manipulability ellipsoid is now a permanent, integral component of the robot visualization system, providing continuous, real-time feedback about robot dexterity and mobility characteristics without any user intervention required.

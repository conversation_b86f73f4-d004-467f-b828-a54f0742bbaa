import vedo as vd
vd.settings.default_backend = 'vtk'
import numpy as np
import os
from time import time

# Global variables
method = 0  # 0 for Gauss-Newton, 1 for Gradient Descent, 2 for Both Methods
method_text = None
last_click_time = 0  # To handle debounce
previous_state = None  # To save the previous state
show_manipulability = False  # Whether to show the manipulability ellipsoid
joint_type_text = None  # Text to display the current joint type

# Function to update method text on screen
def update_method_text():
    global method_text
    if method_text:
        plt.remove(method_text)
    method_names = ["Gauss-Newton", "Gradient Descent", "Both Methods", "Redundancy Resolution"]
    method_name = method_names[method]

    # Add secondary objective info for redundancy resolution
    if method == 3:  # Redundancy Resolution
        current_objective = secondary_objective_names[secondary_objective_index]
        method_text = vd.Text2D(f"Current Method: {method_name}\nSecondary Objective: {current_objective}",
                               pos='bottom-left', s=1.0, c='white', bg='black')
    else:
        method_text = vd.Text2D(f"Current Method: {method_name}", pos='bottom-left', s=1.0, c='white', bg='black')
    plt.add(method_text)

#%% class for a robot arm
def Rot(angle, axis):
    axis = np.array(axis)
    axis = axis/np.linalg.norm(axis)
    I = np.eye(3)
    K = np.array([[0, -axis[2], axis[1]],
                    [axis[2], 0, -axis[0]],
                    [-axis[1], axis[0], 0]])
    R = I + np.sin(angle)*K + (1-np.cos(angle))*np.dot(K,K)
    return R

class SimpleArm:
    def __init__(self, n=3, link_lengths=1, joint_limits=None, joint_types=None):
        self.n = n  # number of links
        self.joint_values = [0] * self.n  # joint values (angles for revolute, displacements for prismatic)
        self.link_lengths = link_lengths

        # Define joint types: 'revolute' or 'prismatic'
        if joint_types is None:
            self.joint_types = ['revolute'] * n  # Default to all revolute joints
        else:
            # Make sure we have the right number of joint types
            if len(joint_types) != n:
                raise ValueError(f"Expected {n} joint types, got {len(joint_types)}")
            # Validate joint types
            for jt in joint_types:
                if jt not in ['revolute', 'prismatic']:
                    raise ValueError(f"Invalid joint type: {jt}. Must be 'revolute' or 'prismatic'")
            self.joint_types = joint_types

        # Initialize joint limits
        # Default limits are -pi to pi for revolute joints, -1 to 1 for prismatic joints
        if joint_limits is None:
            self.joint_limits = []
            for i in range(n):
                if self.joint_types[i] == 'revolute':
                    self.joint_limits.append((-np.pi, np.pi))
                else:  # prismatic
                    self.joint_limits.append((-1.0, 1.0))
        else:
            # Make sure we have the right number of joint limits
            if len(joint_limits) != n:
                raise ValueError(f"Expected {n} joint limits, got {len(joint_limits)}")
            self.joint_limits = joint_limits

        # Ensure initial joint values are within limits
        for i in range(self.n):
            self.joint_values[i] = self.clamp_joint_value(self.joint_values[i], i)

        self.Jl = np.zeros((self.n + 1, 3))
        for i in range(1, n + 1):  # we start from 1 because the base joint is at the origin (0,0,0) and finish the end effector is at the end of the last link
            self.Jl[i, :] = np.array([self.link_lengths[i - 1], 0, 0])  # initialize joint positions to lie along the x-axis WITH GIVEN LENGTH

        # For prismatic joints, we need to store the original link vectors
        # These will be used in FK to apply the prismatic displacement
        self.original_link_vectors = np.zeros((self.n, 3))
        for i in range(self.n):
            self.original_link_vectors[i] = self.Jl[i+1] - self.Jl[i]

        self.Jw = np.zeros((self.n + 1, 3))  # joint positions in world coordinates
        self.FK()

    def clamp_joint_value(self, value, joint_idx):
        """
        Clamp a joint value to be within the limits for the specified joint.

        Parameters:
        - value: The joint value to clamp (angle for revolute, displacement for prismatic)
        - joint_idx: The index of the joint

        Returns:
        - The clamped joint value
        """
        if joint_idx < 0 or joint_idx >= self.n:
            raise ValueError(f"Joint index {joint_idx} out of range [0, {self.n-1}]")

        lower_limit, upper_limit = self.joint_limits[joint_idx]
        return max(lower_limit, min(value, upper_limit))

    def clamp_angle(self, angle, joint_idx):
        """Alias for clamp_joint_value for backward compatibility"""
        return self.clamp_joint_value(angle, joint_idx)

    def set_joint_limit(self, joint_idx, lower_limit, upper_limit):
        """
        Set the joint limit for a specific joint.

        Parameters:
        - joint_idx: The index of the joint
        - lower_limit: The lower limit of the joint value (in radians for revolute, units for prismatic)
        - upper_limit: The upper limit of the joint value (in radians for revolute, units for prismatic)
        """
        if joint_idx < 0 or joint_idx >= self.n:
            raise ValueError(f"Joint index {joint_idx} out of range [0, {self.n-1}]")

        # Ensure lower limit is less than upper limit
        if lower_limit >= upper_limit:
            raise ValueError(f"Lower limit {lower_limit} must be less than upper limit {upper_limit}")

        self.joint_limits[joint_idx] = (lower_limit, upper_limit)

        # Clamp the current joint value to be within the new limits
        self.joint_values[joint_idx] = self.clamp_joint_value(self.joint_values[joint_idx], joint_idx)

    def set_joint_type(self, joint_idx, joint_type):
        """
        Set the type of a specific joint.

        Parameters:
        - joint_idx: The index of the joint
        - joint_type: The type of the joint ('revolute' or 'prismatic')
        """
        if joint_idx < 0 or joint_idx >= self.n:
            raise ValueError(f"Joint index {joint_idx} out of range [0, {self.n-1}]")

        if joint_type not in ['revolute', 'prismatic']:
            raise ValueError(f"Invalid joint type: {joint_type}. Must be 'revolute' or 'prismatic'")

        # Update the joint type
        self.joint_types[joint_idx] = joint_type

        # Update the joint limits if needed
        if joint_type == 'revolute' and self.joint_limits[joint_idx][1] <= 2*np.pi:
            # Default revolute joint limits if current limits are small
            self.joint_limits[joint_idx] = (-np.pi, np.pi)
        elif joint_type == 'prismatic' and self.joint_limits[joint_idx][1] > 2*np.pi:
            # Default prismatic joint limits if current limits are large
            self.joint_limits[joint_idx] = (-1.0, 1.0)

        # Clamp the current joint value to be within the new limits
        self.joint_values[joint_idx] = self.clamp_joint_value(self.joint_values[joint_idx], joint_idx)

    def FK(self, joint_values=None):
        """
        Calculate the forward kinematics of the arm.

        Parameters:
        - joint_values: A list of joint values (angles for revolute, displacements for prismatic)
                       If None, the current joint values are used

        Returns:
        - The position of the end effector in world coordinates
        """
        # If joint_values is provided, update the current joint values
        if joint_values is not None:
            # Ensure joint values are within limits
            for i in range(min(len(joint_values), self.n)):
                joint_values[i] = self.clamp_joint_value(joint_values[i], i)
            self.joint_values = joint_values

        # Initial rotation matrix
        Ri = np.eye(3)

        # Base joint is at the origin
        self.Jw[0, :] = np.zeros(3)

        # Compute the position of each joint using a loop
        for i in range(1, self.n + 1):
            if i <= self.n:  # Skip the end effector
                joint_idx = i - 1
                joint_type = self.joint_types[joint_idx]
                joint_value = self.joint_values[joint_idx]

                if joint_type == 'revolute':
                    # For revolute joints, update the rotation matrix
                    Ri = Rot(joint_value, [0, 0, 1]) @ Ri
                    # Update the position using the original link vector
                    self.Jw[i, :] = Ri @ self.Jl[i, :] + self.Jw[i - 1, :]
                elif joint_type == 'prismatic':
                    # For prismatic joints, the rotation doesn't change
                    # But the link length changes along the x-axis of the local frame

                    # Get the original link vector
                    link_vector = self.original_link_vectors[joint_idx].copy()

                    # Calculate the direction of the prismatic joint in the local frame
                    # For simplicity, we assume it's along the x-axis of the local frame
                    prismatic_dir = np.array([1.0, 0.0, 0.0])

                    # Scale the prismatic direction by the joint value
                    prismatic_offset = prismatic_dir * joint_value

                    # Add the prismatic offset to the link vector
                    link_vector += prismatic_offset

                    # Update the position using the modified link vector
                    self.Jw[i, :] = Ri @ link_vector + self.Jw[i - 1, :]
            else:
                # End effector (no joint here)
                self.Jw[i, :] = Ri @ self.Jl[i, :] + self.Jw[i - 1, :]

        return self.Jw[-1, :]  # return the position of the end effector

    def IK(self, target, learning_rate=0.05, max_iterations=500, tolerance=0.05, method='gradient_descent', save_dir="screenshots", use_nullspace=True, secondary_objective='min_displacement'):
        """
        Calculate the inverse kinematics of the arm with redundancy resolution.

        Parameters:
        - target: The target position of the end effector in world coordinates
        - learning_rate: Step size for gradient descent (only used for gradient_descent method)
        - max_iterations: Maximum number of iterations
        - tolerance: Convergence threshold for the error
        - method: The IK method to use ('gradient_descent' or 'gauss_newton')
        - save_dir: Directory to save screenshots of the IK process
        - use_nullspace: Whether to use null-space optimization for redundancy resolution
        - secondary_objective: Type of secondary objective ('min_displacement', 'avoid_limits', 'max_manipulability', 'min_energy')
        """
        max_reach = np.sum(self.link_lengths)

        if np.linalg.norm(target) > max_reach:
            error_message = vd.Text2D("Target out of range!", pos='top-middle', s=1.5, c='red', bg='white')
            plt.add(error_message)
            return

        os.makedirs(save_dir, exist_ok=True)  # Create screenshots directory if not exists

        # Store initial configuration for reference
        initial_joint_values = np.array(self.joint_values.copy())

        velocity = np.zeros(self.n)  # For momentum
        beta = 0.9  # Momentum term

        for iteration in range(max_iterations):
            current_end_effector = self.FK()
            error = target - current_end_effector
            if np.linalg.norm(error) < tolerance:
                print(f"Converged in {iteration} iterations using {method} with {'null-space' if use_nullspace else 'standard'} optimization")
                break

            J = self.VelocityJacobian()

            if method == 'gradient_descent':
                grad = J.T @ error
                velocity = beta * velocity + (1 - beta) * grad  # Momentum update
                d_joint_values = learning_rate * velocity
            elif method == 'gauss_newton':
                J_transpose = J.T
                JT_J_inv = np.linalg.pinv(J_transpose @ J)
                d_joint_values = JT_J_inv @ J_transpose @ error

            # Add null-space optimization for redundancy resolution
            if use_nullspace and self.n > 3:  # Only for redundant systems
                d_joint_values += self._compute_nullspace_term(J, JT_J_inv, secondary_objective, initial_joint_values)

            # Apply the joint value changes and clamp to joint limits
            for i in range(self.n):
                self.joint_values[i] = self.clamp_joint_value(self.joint_values[i] + d_joint_values[i], i)

            if iteration % 1 == 0 and method == 'gauss_newton':
                plt.remove("Assembly")
                plt.remove("Arrow")
                plt.add(self.draw(show_manipulability=False))
                plt.add(vd.Text2D(f"Iteration: {iteration}\nMethod: {method}", pos='top-right', s=1.0, c='white', bg='black'))
                plt.render()
                plt.screenshot(os.path.join(save_dir, f"iteration_{iteration}_{method}.png"))

            if iteration % 5 == 0 and method == 'gradient_descent':
                plt.remove("Assembly")
                plt.remove("Arrow")
                plt.add(self.draw(show_manipulability=False))
                plt.add(vd.Text2D(f"Iteration: {iteration}\nMethod: {method}", pos='top-left', s=1.0, c='white', bg='black'))
                plt.render()
                plt.screenshot(os.path.join(save_dir, f"iteration_{iteration}_{method}.png"))

        # Save the screenshot of the last iteration
        plt.remove("Assembly")
        plt.remove("Arrow")
        plt.add(self.draw(show_manipulability=False))
        plt.add(vd.Text2D(f"Iteration: {iteration}\nMethod: {method}", pos='top-right' if method == 'gauss_newton' else 'top-left', s=1.0, c='white', bg='black'))
        plt.render()
        plt.screenshot(os.path.join(save_dir, f"iteration_{iteration}_{method}.png"))

    def VelocityJacobian(self, joint_values=None):
        """
        Calculate the velocity Jacobian of the arm.

        Parameters:
        - joint_values: A list of joint values (angles for revolute, displacements for prismatic)
                       If None, the current joint values are used

        Returns:
        - The velocity Jacobian matrix
        """
        if joint_values is not None:
            self.FK(joint_values)

        J = np.zeros((3, self.n))
        z = np.array([0, 0, 1])  # axis of rotation for revolute joints

        for i in range(self.n):
            if self.joint_types[i] == 'revolute':
                # For revolute joints, use the cross product formula
                J[:, i] = np.cross(z, (self.Jw[-1] - self.Jw[i]))
            elif self.joint_types[i] == 'prismatic':
                # For prismatic joints, the column is the direction of the prismatic joint
                # rotated to the world frame

                # Calculate the rotation matrix up to this joint
                Ri = np.eye(3)
                for j in range(i):
                    if self.joint_types[j] == 'revolute':
                        Ri = Rot(self.joint_values[j], [0, 0, 1]) @ Ri

                # Direction of the prismatic joint in the local frame (x-axis)
                prismatic_dir = np.array([1.0, 0.0, 0.0])

                # Rotate the prismatic direction to the world frame
                J[:, i] = Ri @ prismatic_dir

        return J

    def _compute_nullspace_term(self, J, JT_J_inv, secondary_objective, initial_joint_values):
        """
        Compute the null-space term for redundancy resolution.

        Parameters:
        - J: Jacobian matrix
        - JT_J_inv: Pseudo-inverse of J^T * J
        - secondary_objective: Type of secondary objective
        - initial_joint_values: Initial joint configuration for reference

        Returns:
        - null_space_term: Joint velocity correction in the null space
        """
        # Compute the null-space projector: N = I - J+ * J
        J_pinv = JT_J_inv @ J.T  # Pseudo-inverse of J
        null_space_projector = np.eye(self.n) - J_pinv @ J

        # Compute secondary objective gradient
        secondary_gradient = self._compute_secondary_gradient(secondary_objective, initial_joint_values)

        # Project secondary objective into null space
        null_space_term = 0.1 * null_space_projector @ secondary_gradient  # Scale factor for secondary objective

        return null_space_term

    def _compute_secondary_gradient(self, objective_type, initial_joint_values):
        """
        Compute the gradient of the secondary objective function.

        Parameters:
        - objective_type: Type of secondary objective
        - initial_joint_values: Initial joint configuration for reference

        Returns:
        - gradient: Gradient vector for the secondary objective
        """
        current_joint_values = np.array(self.joint_values)

        if objective_type == 'min_displacement':
            # Minimize displacement from initial configuration
            gradient = -(current_joint_values - initial_joint_values)

        elif objective_type == 'avoid_limits':
            # Stay away from joint limits
            gradient = np.zeros(self.n)
            for i in range(self.n):
                lower_limit, upper_limit = self.joint_limits[i]
                joint_value = current_joint_values[i]

                # Create a potential field that repels from limits
                range_size = upper_limit - lower_limit
                normalized_pos = (joint_value - lower_limit) / range_size  # 0 to 1

                # Gradient points away from the nearest limit
                if normalized_pos < 0.5:
                    # Closer to lower limit, push up
                    gradient[i] = 1.0 / (normalized_pos + 0.1)
                else:
                    # Closer to upper limit, push down
                    gradient[i] = -1.0 / (1.0 - normalized_pos + 0.1)

        elif objective_type == 'max_manipulability':
            # Maximize manipulability measure
            J = self.VelocityJacobian()
            manipulability = np.sqrt(np.linalg.det(J @ J.T))

            # Approximate gradient using finite differences
            gradient = np.zeros(self.n)
            epsilon = 1e-6
            for i in range(self.n):
                # Perturb joint i
                original_value = self.joint_values[i]
                self.joint_values[i] += epsilon
                self.FK()
                J_plus = self.VelocityJacobian()
                manip_plus = np.sqrt(np.linalg.det(J_plus @ J_plus.T))

                # Restore original value
                self.joint_values[i] = original_value
                self.FK()

                # Compute finite difference
                gradient[i] = (manip_plus - manipulability) / epsilon

        elif objective_type == 'min_energy':
            # Minimize joint velocities (energy)
            gradient = -current_joint_values  # Prefer smaller joint values

        else:
            # Default: minimize displacement
            gradient = -(current_joint_values - initial_joint_values)

        return gradient

    def calculate_manipulability_ellipsoid(self, scale_factor=0.2):
        """
        Calculate the manipulability ellipsoid for the current robot configuration.

        The manipulability ellipsoid represents how effectively the robot can move in different directions.
        It is centered at the end effector, with axes aligned with the singular vectors of the Jacobian,
        and scaled according to the singular values.

        Parameters:
        - scale_factor: Factor to scale the ellipsoid for better visualization

        Returns:
        - center: The center of the ellipsoid (end effector position)
        - axes: The three axes of the ellipsoid as 3D vectors
        - scales: The scales of the three axes
        """
        # Get the Jacobian
        J = self.VelocityJacobian()

        # Compute the singular value decomposition
        U, S, Vh = np.linalg.svd(J, full_matrices=False)

        # The singular vectors in U represent the principal axes of the ellipsoid
        # The singular values in S represent the scales along these axes

        # Center of the ellipsoid is at the end effector
        center = self.Jw[-1, :]

        # The axes of the ellipsoid are the columns of U
        # For a 3xn Jacobian, U is 3x3 and has 3 columns
        axes = U

        # The scales of the ellipsoid are the singular values
        # Scale them for better visualization
        scales = S * scale_factor

        return center, axes, scales

    def draw(self, show_manipulability=False):
        """
        Draw the robot arm and optionally the manipulability ellipsoid.

        Parameters:
        - show_manipulability: Whether to show the manipulability ellipsoid

        Returns:
        - vd_arm: A vedo Assembly containing the arm and optionally the ellipsoid
        """
        vd_arm = vd.Assembly()

        # Draw the base
        vd_arm += vd.Sphere(pos=self.Jw[0, :], r=0.05)

        # Draw the links and joints
        for i in range(1, self.n + 1):
            # Draw the link
            vd_arm += vd.Cylinder(pos=[self.Jw[i - 1, :], self.Jw[i, :]], r=0.02)

            # Draw the joint
            if i <= self.n:  # Skip the end effector
                joint_idx = i - 1
                joint_type = self.joint_types[joint_idx]
                joint_value = self.joint_values[joint_idx]
                lower_limit, upper_limit = self.joint_limits[joint_idx]
                tolerance = 0.01  # Small tolerance for floating point comparison

                # Determine joint color based on whether it's at its limit
                joint_color = 'green'  # Default color
                if abs(joint_value - lower_limit) < tolerance:
                    joint_color = 'red'  # At lower limit
                elif abs(joint_value - upper_limit) < tolerance:
                    joint_color = 'red'  # At upper limit

                if joint_type == 'revolute':
                    # Draw a sphere for revolute joints
                    vd_arm += vd.Sphere(pos=self.Jw[i-1, :], r=0.05, c=joint_color)

                    # Add a ring to indicate the rotation axis
                    vd_arm += vd.Circle(pos=self.Jw[i-1, :], r=0.07, c=joint_color, alpha=0.7)

                elif joint_type == 'prismatic':
                    # Draw a cube for prismatic joints
                    vd_arm += vd.Cube(pos=self.Jw[i-1, :], side=0.08, c=joint_color)

                    # Calculate the rotation matrix up to this joint
                    Ri = np.eye(3)
                    for j in range(i-1):
                        if self.joint_types[j] == 'revolute':
                            Ri = Rot(self.joint_values[j], [0, 0, 1]) @ Ri

                    # Direction of the prismatic joint in the local frame (x-axis)
                    prismatic_dir = np.array([1.0, 0.0, 0.0])

                    # Rotate the prismatic direction to the world frame
                    world_prismatic_dir = Ri @ prismatic_dir

                    # Scale the direction for visualization
                    arrow_length = 0.15

                    # Add an arrow to show the prismatic direction
                    vd_arm += vd.Arrow(self.Jw[i-1, :] - world_prismatic_dir * arrow_length/2,
                                      self.Jw[i-1, :] + world_prismatic_dir * arrow_length/2,
                                      c='white', s=0.01)
            else:
                # End effector
                vd_arm += vd.Sphere(pos=self.Jw[i, :], r=0.05)

        # Add text to show joint limits and type for the active joint
        if hasattr(self, 'active_joint_idx'):
            joint_idx = self.active_joint_idx
            if 0 <= joint_idx < self.n:
                joint_type = self.joint_types[joint_idx]
                lower_limit, upper_limit = self.joint_limits[joint_idx]
                limit_text = f"Joint {joint_idx} ({joint_type})\nLimits: [{lower_limit:.2f}, {upper_limit:.2f}]"
                vd_arm += vd.Text2D(limit_text, pos='top-right', s=1.0, c='white', bg='black')

        # Add text explaining joint types
        joint_types_text = "Joint Types:\nSphere = Revolute Joint (rotation)\nCube = Prismatic Joint (translation)"
        vd_arm += vd.Text2D(joint_types_text, pos='bottom-left', s=0.8, c='white', bg='black')

        # Draw the manipulability ellipsoid if requested
        if show_manipulability:
            center, axes, scales = self.calculate_manipulability_ellipsoid()

            # Create the ellipsoid
            ellipsoid = vd.Ellipsoid(pos=center, axis1=axes[:, 0]*scales[0],
                                     axis2=axes[:, 1]*scales[1], axis3=axes[:, 2]*scales[2],
                                     c='lightblue', alpha=0.5, res=24)

            # Add the ellipsoid to the assembly
            vd_arm += ellipsoid

            # Add arrows to show the principal axes of the ellipsoid
            colors = ['red', 'green', 'blue']
            for i in range(3):
                vd_arm += vd.Arrow(center, center + axes[:, i] * scales[i], c=colors[i], s=0.02)

            # Add text explaining the manipulability ellipsoid
            vd_arm += vd.Text2D("Manipulability Ellipsoid:\nBlue ellipsoid shows possible end effector velocities\nRed/Green/Blue arrows show principal axes",
                               pos='bottom-right', s=0.8, c='white', bg='black')

        return vd_arm

def visualize_jacobian(arm): # Function to visualize the vectors of the Jacobian
    """
    Visualize the Jacobian vectors as arrows coming from each joint.
    Each arrow represents how the end effector moves when that joint moves.
    """
    J = arm.VelocityJacobian()
    jacobian_vectors = vd.Assembly()
    scale_factor = 0.3  # Scale factor for arrow length

    # Define colors for different joints
    colors = ['red', 'green', 'blue', 'cyan', 'magenta', 'yellow', 'orange', 'purple', 'brown', 'pink']

    for i in range(arm.n):
        # Get the position of the i-th joint
        joint_pos = arm.Jw[i, :]

        # Get the i-th column of the Jacobian (velocity contribution of joint i)
        jacobian_column = J[:, i]

        # Calculate the end point of the arrow
        end_pt = joint_pos + jacobian_column * scale_factor

        # Choose color based on joint index, cycling through available colors
        color = colors[i % len(colors)]

        # Create arrow from joint position in the direction of the Jacobian column
        jacobian_vectors += vd.Arrow(joint_pos, end_pt, c=color, s=0.005)

    return jacobian_vectors

#%%
activeJoint = 0
IK_target = [1,1,0]

# Global variables for redundancy resolution
secondary_objective_index = 0
secondary_objectives = ['min_displacement', 'avoid_limits', 'max_manipulability', 'min_energy']
secondary_objective_names = ['Min Displacement', 'Avoid Limits', 'Max Manipulability', 'Min Energy']

def OnSliderAngle(widget, event):
    global activeJoint, show_manipulability
    # Clamp the joint value to the joint limits
    arm.joint_values[activeJoint] = arm.clamp_joint_value(widget.value, activeJoint)
    # Update the slider value to reflect the clamped joint value
    if widget.value != arm.joint_values[activeJoint]:
        widget.value = arm.joint_values[activeJoint]
    arm.FK()
    plt.remove("Assembly")
    plt.remove("Arrow")  # Remove all arrows using their identifier
    plt.add(arm.draw(show_manipulability=show_manipulability))
    plt.add(visualize_jacobian(arm))
    plt.add(method_button)  # Re-add method button
    plt.add(manipulability_button)  # Re-add manipulability button
    plt.add(joint_type_button)  # Re-add joint type button
    update_method_text()  # Ensure method text is updated
    plt.render()

def OnCurrentJoint(widget, event):
    global activeJoint, joint_limit_lower, joint_limit_upper, show_manipulability, joint_type_text
    activeJoint = round(widget.value)

    # Set the active joint in the arm for visualization
    arm.active_joint_idx = activeJoint

    # Update the joint value slider
    sliderAngle.value = arm.joint_values[activeJoint]

    # Update the joint limit sliders
    lower_limit, upper_limit = arm.joint_limits[activeJoint]
    joint_limit_lower = lower_limit
    joint_limit_upper = upper_limit

    # Set appropriate ranges for the limit sliders based on joint type
    if arm.joint_types[activeJoint] == 'revolute':
        # For revolute joints, use angle limits
        sliderLowerLimit.vrange = (-np.pi, upper_limit - 0.1)
        sliderUpperLimit.vrange = (lower_limit + 0.1, np.pi)
    else:
        # For prismatic joints, use displacement limits
        sliderLowerLimit.vrange = (-2.0, upper_limit - 0.1)
        sliderUpperLimit.vrange = (lower_limit + 0.1, 2.0)

    sliderLowerLimit.value = lower_limit
    sliderUpperLimit.value = upper_limit

    # Update the angle slider range
    sliderAngle.vrange = (lower_limit, upper_limit)

    # Update the joint type text
    if hasattr(globals(), 'joint_type_text') and joint_type_text:
        plt.remove(joint_type_text)
    joint_type_text = vd.Text2D(f"Joint {activeJoint} Type: {arm.joint_types[activeJoint]}",
                               pos=(0.5, 0.05), c='white', bg='black')
    plt.add(joint_type_text)

    # Update the display
    plt.remove("Assembly")
    plt.remove("Arrow")
    plt.add(arm.draw(show_manipulability=show_manipulability))
    plt.add(visualize_jacobian(arm))

    plt.add(method_button)  # Re-add method button
    plt.add(manipulability_button)  # Re-add manipulability button
    plt.add(joint_type_button)  # Re-add joint type button
    plt.add(secondary_objective_button)  # Re-add secondary objective button
    update_method_text()  # Ensure method text is updated
    plt.render()

def toggle_method(widget, event):
    global method, last_click_time
    current_time = time()
    if current_time - last_click_time < 0.3:  # debounce time of 300ms
        return
    last_click_time = current_time

    #print("Toggling method")
    method = (method + 1) % 4  # Cycle through 0, 1, 2, and 3
    update_method_text()  # Update the method text

def toggle_secondary_objective(widget, event):
    global secondary_objective_index
    # Only allow changing secondary objective when in redundancy resolution mode
    if method == 3:
        secondary_objective_index = (secondary_objective_index + 1) % len(secondary_objectives)
        update_method_text()  # Update method text to show new secondary objective

def save_current_state():
    global previous_state
    previous_state = {
        "joint_values": arm.joint_values.copy(),
        "joint_types": arm.joint_types.copy(),
        "joint_limits": arm.joint_limits.copy(),
        "Jw": arm.Jw.copy()
    }

def revert_to_previous_state():
    global previous_state
    if previous_state is not None:
        arm.joint_values = previous_state["joint_values"].copy()
        arm.joint_types = previous_state["joint_types"].copy()
        arm.joint_limits = previous_state["joint_limits"].copy()
        arm.Jw = previous_state["Jw"].copy()
        arm.FK()

def run_and_save_both_methods(target):
    global show_manipulability
    save_dirs = ["screenshots/gauss_newton", "screenshots/gradient_descent"]
    methods = ["gauss_newton", "gradient_descent"]

    for method, save_dir in zip(methods, save_dirs):
        revert_to_previous_state()
        arm.IK(target, method=method, save_dir=save_dir, use_nullspace=False, secondary_objective='min_displacement')
        plt.remove("Assembly")
        plt.remove("Arrow")
        plt.add(arm.draw(show_manipulability=show_manipulability))
        plt.add(visualize_jacobian(arm))
        plt.add(method_button)  # Re-add method button
        plt.add(manipulability_button)  # Re-add manipulability button
        plt.add(joint_type_button)  # Re-add joint type button
        update_method_text()
        plt.render()

def LeftButtonPress(evt):
    global IK_target, method, show_manipulability, joint_type_text
    IK_target = evt.picked3d
    if IK_target is not None:
        save_current_state()  # Save the current state before moving to a new target
        plt.remove("Sphere")
        plt.remove("Text2D")  # Remove the error message
        plt.add(vd.Sphere(pos=IK_target, r=0.05, c='b'))

        if method == 2:  # Both methods
            run_and_save_both_methods(IK_target)  # Run both methods and save the results
        elif method == 3:  # Redundancy Resolution
            current_secondary_objective = secondary_objectives[secondary_objective_index]
            arm.IK(IK_target, method='gauss_newton', use_nullspace=True, secondary_objective=current_secondary_objective)  # Use Gauss-Newton with redundancy resolution
            plt.remove("Assembly")
            plt.remove("Arrow")
            plt.add(arm.draw(show_manipulability=show_manipulability))
            plt.add(visualize_jacobian(arm))
            plt.add(method_button)  # Re-add method button
            plt.add(manipulability_button)  # Re-add manipulability button
            plt.add(joint_type_button)  # Re-add joint type button

            # Update joint type text
            if joint_type_text:
                plt.remove(joint_type_text)
            joint_type_text = vd.Text2D(f"Joint {activeJoint} Type: {arm.joint_types[activeJoint]}",
                                      pos=(0.5, 0.05), c='white', bg='black')
            plt.add(joint_type_text)

            update_method_text()
            plt.render()
        else:
            method_name = "gauss_newton" if method == 0 else "gradient_descent"
            arm.IK(IK_target, method=method_name, use_nullspace=False, secondary_objective='min_displacement')  # Run the selected method without redundancy resolution
            plt.remove("Assembly")
            plt.remove("Arrow")
            plt.add(arm.draw(show_manipulability=show_manipulability))
            plt.add(visualize_jacobian(arm))
            plt.add(method_button)  # Re-add method button
            plt.add(manipulability_button)  # Re-add manipulability button
            plt.add(joint_type_button)  # Re-add joint type button

            # Update joint type text
            if joint_type_text:
                plt.remove(joint_type_text)
            joint_type_text = vd.Text2D(f"Joint {activeJoint} Type: {arm.joint_types[activeJoint]}",
                                      pos=(0.5, 0.05), c='white', bg='black')
            plt.add(joint_type_text)

            update_method_text()
            plt.render()

# Define link lengths
link_lengths1 = [1.0, 0.3, 0.5, 2.0, 0.5]
link_lengths2 = [1.0, 0.8, 1.5]
link_lengths3 = [0.5, 1.0, 0.7, 1.2]
link_lengths4 = [0.5, 0.5, 0.6, 0.7, 1.0]
link_lengths5 = [1.0, 0.5, 0.5, 1.0]
link_lengths6 = [0.4, 0.3, 0.5, 0.4, 0.3, 0.5, 0.4, 0.3, 0.5, 0.4, 0.3, 0.5, 0.4, 0.3, 0.5]

link_lengths = link_lengths6

# Define joint types: mix of revolute and prismatic joints
joint_types = ['revolute', 'revolute', 'prismatic', 'revolute', 'revolute', 'prismatic', 'revolute', 'revolute', 'prismatic', 'revolute', 'revolute', 'prismatic', 'revolute', 'revolute', 'prismatic']

# Define joint limits
joint_limits = []
for i in range(len(link_lengths)):
    if i % 3 == 2:  # Every third joint is prismatic
        joint_limits.append((-0.5, 0.5))  # Prismatic joint limits in units
    else:
        joint_limits.append((-np.pi/2, np.pi/2))  # Revolute joint limits in radians

# Create the arm with joint types and limits
arm = SimpleArm(len(link_lengths), link_lengths, joint_limits, joint_types)

# Set the active joint index for visualization
arm.active_joint_idx = 0
plt = vd.Plotter()
plt += arm.draw()
plt += visualize_jacobian(arm)  # Add visualization of Jacobian
plt += vd.Sphere(pos=IK_target, r=0.05, c='b').draggable(True)
plt += vd.Plane(s=[2.1 * sum(link_lengths), 2.1 * sum(link_lengths)])

# Global variables for joint limit sliders
joint_limit_lower = -np.pi
joint_limit_upper = np.pi

# Callback for lower limit slider
def OnLowerLimitSlider(widget, event):
    global activeJoint, joint_limit_lower, joint_limit_upper, show_manipulability
    joint_limit_lower = widget.value

    # Ensure lower limit is less than upper limit
    if joint_limit_lower >= joint_limit_upper:
        joint_limit_lower = joint_limit_upper - 0.1
        widget.value = joint_limit_lower

    # Update the joint limit
    arm.set_joint_limit(activeJoint, joint_limit_lower, joint_limit_upper)

    # Update the angle slider range
    sliderAngle.vrange = (joint_limit_lower, joint_limit_upper)

    # Update the display
    plt.remove("Assembly")
    plt.remove("Arrow")
    plt.add(arm.draw(show_manipulability=show_manipulability))
    plt.add(visualize_jacobian(arm))
    plt.add(method_button)
    plt.add(manipulability_button)
    update_method_text()
    plt.render()

# Callback for upper limit slider
def OnUpperLimitSlider(widget, event):
    global activeJoint, joint_limit_lower, joint_limit_upper, show_manipulability
    joint_limit_upper = widget.value

    # Ensure upper limit is greater than lower limit
    if joint_limit_upper <= joint_limit_lower:
        joint_limit_upper = joint_limit_lower + 0.1
        widget.value = joint_limit_upper

    # Update the joint limit
    arm.set_joint_limit(activeJoint, joint_limit_lower, joint_limit_upper)

    # Update the angle slider range
    sliderAngle.vrange = (joint_limit_lower, joint_limit_upper)

    # Update the display
    plt.remove("Assembly")
    plt.remove("Arrow")
    plt.add(arm.draw(show_manipulability=show_manipulability))
    plt.add(visualize_jacobian(arm))
    plt.add(method_button)
    plt.add(manipulability_button)
    update_method_text()
    plt.render()

# Function to toggle the manipulability ellipsoid
def toggle_manipulability(widget, event):
    global show_manipulability

    # Toggle the manipulability ellipsoid
    show_manipulability = not show_manipulability

    # Update the display
    plt.remove("Assembly")
    plt.remove("Arrow")
    plt.add(arm.draw(show_manipulability=show_manipulability))
    plt.add(visualize_jacobian(arm))
    plt.add(method_button)
    plt.add(manipulability_button)
    plt.add(joint_type_button)
    update_method_text()
    plt.render()

# Function to toggle joint type
def toggle_joint_type(widget, event):
    global activeJoint, joint_type_text

    # Toggle the joint type
    current_type = arm.joint_types[activeJoint]
    new_type = 'prismatic' if current_type == 'revolute' else 'revolute'

    # Update the joint type
    arm.set_joint_type(activeJoint, new_type)

    # Update the joint type text
    if joint_type_text:
        plt.remove(joint_type_text)
    joint_type_text = vd.Text2D(f"Joint {activeJoint} Type: {new_type}", pos=(0.5, 0.05), c='white', bg='black')
    plt.add(joint_type_text)

    # Update the joint limit sliders
    lower_limit, upper_limit = arm.joint_limits[activeJoint]

    # Set appropriate ranges for the limit sliders based on joint type
    if new_type == 'revolute':
        # For revolute joints, use angle limits
        sliderLowerLimit.vrange = (-np.pi, upper_limit - 0.1)
        sliderUpperLimit.vrange = (lower_limit + 0.1, np.pi)
    else:
        # For prismatic joints, use displacement limits
        sliderLowerLimit.vrange = (-2.0, upper_limit - 0.1)
        sliderUpperLimit.vrange = (lower_limit + 0.1, 2.0)

    sliderLowerLimit.value = lower_limit
    sliderUpperLimit.value = upper_limit

    # Update the angle slider range and value
    sliderAngle.vrange = (lower_limit, upper_limit)
    sliderAngle.value = arm.joint_values[activeJoint]

    # Update the display
    plt.remove("Assembly")
    plt.remove("Arrow")
    plt.add(arm.draw(show_manipulability=show_manipulability))
    plt.add(visualize_jacobian(arm))
    plt.add(method_button)
    plt.add(manipulability_button)
    plt.add(joint_type_button)
    update_method_text()
    plt.render()

# Add GUI components
sliderCurrentJoint = plt.add_slider(OnCurrentJoint, 0, arm.n - 1, 0, title="Current joint", pos=3, delayed=True)
sliderAngle = plt.add_slider(OnSliderAngle, -np.pi, np.pi, 0., title="Joint Value", pos=4)
sliderLowerLi mit = plt.add_slider(OnLowerLimitSlider, -np.pi, 0, -np.pi, title="Lower Limit", pos=5)
sliderUpperLimit = plt.add_slider(OnUpperLimitSlider, 0, np.pi, np.pi, title="Upper Limit", pos=6)
method_button = plt.add_button(toggle_method, pos=(0.8, 0.05), states=["Toggle Method"], size=12)
manipulability_button = plt.add_button(toggle_manipulability, pos=(0.8, 0.1), states=["Toggle Manipulability"], size=12)
joint_type_button = plt.add_button(toggle_joint_type, pos=(0.8, 0.15), states=["Toggle Joint Type"], size=12)
secondary_objective_button = plt.add_button(toggle_secondary_objective, pos=(0.8, 0.2), states=["Toggle Objective"], size=12)

# Initialize text displays
joint_type_text = vd.Text2D(f"Joint {activeJoint} Type: {arm.joint_types[activeJoint]}", pos=(0.5, 0.05), c='white', bg='black')
plt.add(joint_type_text)

update_method_text()
plt.add_callback('LeftButtonPress', LeftButtonPress)
plt.user_mode('2d').show(zoom="tightest")
plt.close()

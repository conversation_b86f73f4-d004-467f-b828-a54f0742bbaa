import vedo as vd
vd.settings.default_backend = 'vtk'
import numpy as np
import os

# Import the SimpleArm class from the main file
exec(open('chat code.py').read())

def test_manipulability_configurations():
    """
    Test the manipulability ellipsoid in different robot configurations.
    This demonstrates Task 4.2 requirements.
    """
    
    # Create a simpler arm for testing
    link_lengths = [1.0, 0.8, 0.6]
    joint_types = ['revolute', 'revolute', 'revolute']
    joint_limits = [(-np.pi, np.pi), (-np.pi, np.pi), (-np.pi, np.pi)]
    
    arm = SimpleArm(len(link_lengths), link_lengths, joint_limits, joint_types)
    
    # Test configurations
    configurations = [
        {
            'name': 'Straight Arm',
            'angles': [0, 0, 0],
            'description': 'All joints at zero - elongated ellipsoid'
        },
        {
            'name': 'L-shaped Arm', 
            'angles': [0, np.pi/2, 0],
            'description': 'Second joint at 90° - more balanced ellipsoid'
        },
        {
            'name': 'S-shaped Arm',
            'angles': [np.pi/4, -np.pi/3, np.pi/6],
            'description': 'Complex configuration - unique ellipsoid shape'
        },
        {
            'name': 'Singular Configuration',
            'angles': [0, 0, np.pi],
            'description': 'Third joint folded back - compressed ellipsoid'
        },
        {
            'name': 'Near-Singular Configuration',
            'angles': [0, 0, np.pi - 0.1],
            'description': 'Almost singular - highly compressed ellipsoid'
        }
    ]
    
    # Create screenshots directory
    os.makedirs('manipulability_screenshots', exist_ok=True)
    
    for i, config in enumerate(configurations):
        print(f"\nTesting Configuration {i+1}: {config['name']}")
        print(f"Description: {config['description']}")
        print(f"Joint angles: {config['angles']}")
        
        # Set the arm configuration
        arm.FK(config['angles'])
        
        # Calculate manipulability ellipsoid
        center, axes, scales = arm.calculate_manipulability_ellipsoid()
        
        # Print manipulability information
        J = arm.VelocityJacobian()
        manipulability = np.sqrt(np.linalg.det(J @ J.T)) if J.shape[0] == J.shape[1] else np.prod(scales)
        
        print(f"Manipulability measure: {manipulability:.6f}")
        print(f"Singular values: {scales}")
        print(f"End effector position: {center}")
        
        # Create visualization
        plt = vd.Plotter(offscreen=True, size=(1200, 800))
        
        # Add the arm
        plt.add(arm.draw(show_manipulability=True))
        
        # Add Jacobian visualization
        plt.add(visualize_jacobian(arm))
        
        # Add configuration title
        title_text = vd.Text2D(f"Task 4.2: {config['name']}\n{config['description']}", 
                              pos='top-center', s=1.2, c='yellow', bg='black')
        plt.add(title_text)
        
        # Add a plane for reference
        plt.add(vd.Plane(s=[6, 6], c='lightgray', alpha=0.1))
        
        # Set camera position for better view
        plt.camera.SetPosition([4, 4, 3])
        plt.camera.SetFocalPoint([0, 0, 0])
        plt.camera.SetViewUp([0, 0, 1])
        
        # Save screenshot
        screenshot_path = f'manipulability_screenshots/config_{i+1}_{config["name"].replace(" ", "_").lower()}.png'
        plt.screenshot(screenshot_path)
        print(f"Screenshot saved: {screenshot_path}")
        
        plt.close()
    
    print(f"\n✅ Task 4.2 Testing Complete!")
    print(f"📸 Screenshots saved in 'manipulability_screenshots/' directory")
    print(f"🎯 Manipulability Ellipsoid successfully implemented and tested")

if __name__ == "__main__":
    test_manipulability_configurations()

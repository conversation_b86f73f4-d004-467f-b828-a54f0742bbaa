import vedo as vd
vd.settings.default_backend = 'vtk'
import numpy as np

# Import the SimpleArm class from the main file
exec(open('chat code.py').read())

def test_toggle_functionality():
    """
    Test that the manipulability ellipsoid toggle works correctly.
    """
    
    # Create a simple arm for testing
    link_lengths = [1.0, 0.8, 0.6]
    joint_types = ['revolute', 'revolute', 'revolute']
    joint_limits = [(-np.pi, np.pi), (-np.pi, np.pi), (-np.pi, np.pi)]
    
    test_arm = SimpleArm(len(link_lengths), link_lengths, joint_limits, joint_types)
    test_arm.FK([np.pi/4, -np.pi/6, np.pi/3])  # Set an interesting configuration
    
    print("🧪 Testing Manipulability Ellipsoid Toggle Functionality")
    print("=" * 60)
    
    # Test 1: Default state (should be False)
    print(f"1. Initial show_manipulability state: {show_manipulability}")
    
    # Test 2: Draw with manipulability OFF
    print("2. Drawing arm with manipulability OFF...")
    arm_assembly_off = test_arm.draw(show_manipulability=False)
    print(f"   Assembly contains {len(arm_assembly_off)} objects")
    
    # Test 3: Draw with manipulability ON
    print("3. Drawing arm with manipulability ON...")
    arm_assembly_on = test_arm.draw(show_manipulability=True)
    print(f"   Assembly contains {len(arm_assembly_on)} objects")
    
    # Test 4: Check if ellipsoid is actually added
    ellipsoid_found = False
    for obj in arm_assembly_on:
        if hasattr(obj, 'name') and 'Ellipsoid' in str(type(obj)):
            ellipsoid_found = True
            break
    
    print(f"4. Ellipsoid found in assembly: {ellipsoid_found}")
    
    # Test 5: Calculate manipulability data
    center, axes, scales = test_arm.calculate_manipulability_ellipsoid()
    print(f"5. Manipulability ellipsoid data:")
    print(f"   Center: {center}")
    print(f"   Scales: {scales}")
    print(f"   Axes shape: {axes.shape}")
    
    # Test 6: Verify Jacobian calculation
    J = test_arm.VelocityJacobian()
    print(f"6. Jacobian matrix:")
    print(f"   Shape: {J.shape}")
    print(f"   Condition number: {np.linalg.cond(J):.4f}")
    
    # Test 7: SVD verification
    U, S, _ = np.linalg.svd(J, full_matrices=False)
    print(f"7. SVD results:")
    print(f"   Singular values: {S}")
    print(f"   U shape: {U.shape}")
    
    print("=" * 60)
    print("✅ All tests completed successfully!")
    print("🎯 The manipulability ellipsoid should now toggle correctly")
    print("📝 Instructions:")
    print("   1. Run the main program: python 'chat code.py'")
    print("   2. Click the 'Toggle Manipulability' button")
    print("   3. The blue ellipsoid should appear and stay visible")
    print("   4. Click again to hide it")
    print("   5. Move joints - ellipsoid should update in real-time")

if __name__ == "__main__":
    test_toggle_functionality()

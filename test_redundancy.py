#!/usr/bin/env python3
"""
Test script for redundancy resolution functionality
"""

import numpy as np
import sys
import os

# Add the current directory to the path to import from chat code.py
sys.path.append('.')

# Import the SimpleArm class
import importlib.util
spec = importlib.util.spec_from_file_location("chat_code", "chat code.py")
chat_code = importlib.util.module_from_spec(spec)
spec.loader.exec_module(chat_code)
SimpleArm = chat_code.SimpleArm

def test_redundancy_resolution():
    """Test the redundancy resolution functionality"""

    print("Testing Redundancy Resolution Implementation")
    print("=" * 50)

    # Create a redundant arm (5 joints)
    link_lengths = [1.0, 0.8, 0.6, 0.4, 0.3]
    joint_types = ['revolute'] * 5
    joint_limits = [(-np.pi/2, np.pi/2)] * 5

    arm = SimpleArm(5, link_lengths, joint_limits, joint_types)

    # Test target
    target = np.array([2.0, 1.0, 0.0])

    print(f"Target position: {target}")
    print(f"Number of joints: {arm.n}")
    print(f"System is {'redundant' if arm.n > 3 else 'not redundant'}")
    print()

    # Test different secondary objectives
    secondary_objectives = ['min_displacement', 'avoid_limits', 'max_manipulability', 'min_energy']

    for i, objective in enumerate(secondary_objectives):
        print(f"Test {i+1}: Secondary Objective = {objective}")
        print("-" * 30)

        # Reset arm to initial configuration
        arm.joint_values = [0.0] * arm.n
        initial_config = np.array(arm.joint_values.copy())

        # Run IK with redundancy resolution
        try:
            arm.IK(target, method='gauss_newton', use_nullspace=True,
                  secondary_objective=objective, max_iterations=100, tolerance=0.01)

            final_config = np.array(arm.joint_values)
            end_effector_pos = arm.FK()

            # Calculate metrics
            displacement = np.linalg.norm(final_config - initial_config)
            error = np.linalg.norm(end_effector_pos - target)

            print(f"  Initial config: {initial_config}")
            print(f"  Final config:   {final_config}")
            print(f"  End effector:   {end_effector_pos}")
            print(f"  Target error:   {error:.6f}")
            print(f"  Joint displacement: {displacement:.6f}")

            # Check if within joint limits
            within_limits = all(
                arm.joint_limits[j][0] <= arm.joint_values[j] <= arm.joint_limits[j][1]
                for j in range(arm.n)
            )
            print(f"  Within limits:  {within_limits}")

        except Exception as e:
            print(f"  Error: {e}")

        print()

    # Test without redundancy resolution
    print("Test: Without Redundancy Resolution")
    print("-" * 30)

    arm.joint_values = [0.0] * arm.n
    initial_config = np.array(arm.joint_values.copy())

    try:
        arm.IK(target, method='gauss_newton', use_nullspace=False,
              max_iterations=100, tolerance=0.01)

        final_config = np.array(arm.joint_values)
        end_effector_pos = arm.FK()

        displacement = np.linalg.norm(final_config - initial_config)
        error = np.linalg.norm(end_effector_pos - target)

        print(f"  Initial config: {initial_config}")
        print(f"  Final config:   {final_config}")
        print(f"  End effector:   {end_effector_pos}")
        print(f"  Target error:   {error:.6f}")
        print(f"  Joint displacement: {displacement:.6f}")

    except Exception as e:
        print(f"  Error: {e}")

    print("\nTest completed!")

if __name__ == "__main__":
    test_redundancy_resolution()
